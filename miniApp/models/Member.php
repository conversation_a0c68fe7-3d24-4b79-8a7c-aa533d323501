<?php

namespace miniApp\models;

use common\base\models\BaseDictionary;
use common\base\models\BaseMember;
use common\base\models\BaseMemberLoginForm;
use common\base\models\BaseResume;
use common\base\models\BaseResumeComplete;
use common\base\models\BaseResumeEducation;
use common\base\models\BaseResumeEquityPackage;
use common\base\models\BaseResumeEquitySetting;
use common\service\resumeRemind\ResumeRemindApplication;

class Member extends BaseMember
{

    /**
     * 获取我的页面信息
     * @param $memberId
     * @return array|\yii\db\ActiveRecord|null
     */
    public static function getMiniAppMemberInfo($memberId)
    {
        $vipTypeActive = BaseResume::VIP_TYPE_ACTIVE;
        $vipTypeNormal = BaseResume::VIP_TYPE_NORMAL;

        //获取用户基本信息
        $memberInfo = self::find()
            ->alias('m')
            ->leftJoin(['r' => BaseResume::tableName()], 'm.id=r.member_id')
            ->where(['m.id' => $memberId])
            ->select([
                'm.avatar',
                'm.username',
                'r.name',
                'r.age',
                'r.last_education_id as lastEducationId',
                'r.work_experience as workExperience',
                'r.work_status as workStatus',
                'r.arrive_date_type as arriveDateType',
                'r.complete',
                'r.vip_type',
                "(CASE r.vip_type WHEN $vipTypeActive THEN $vipTypeActive ELSE $vipTypeNormal END) AS isVip",
                'r.vip_begin_time',
                'r.vip_expire_time',
                'r.vip_level',
                "DATE_FORMAT(r.vip_begin_time, '%Y-%m-%d') AS vipBeginDate",
                "DATE_FORMAT(r.vip_expire_time, '%Y-%m-%d') AS vipExpireDate",
            ])
            ->asArray()
            ->one();
        //获取工作求职状态
        $workStatus = BaseDictionary::getDataName(BaseDictionary::TYPE_JOB_STATUS, $memberInfo['workStatus']);
        //获取到岗时间
        if ($memberInfo['arriveDateType'] != BaseResume::CUSTOM_ARRIVE_DATE_TYPE) {
            $arriveDate = BaseDictionary::getArriveDateName($memberInfo['arriveDateType']);
        } else {
            $arriveDate = $memberInfo['arrive_date'];
        }
        if (!empty($arriveDate)) {
            $arriveDate .= '到岗';
        }

        //补充临时求职意向
        $memberInfo['intentionInfo'] = '';
        if (!empty($workStatus)) {
            $memberInfo['intentionInfo'] .= $workStatus . '-';
        }
        if (!empty($arriveDate)) {
            $memberInfo['intentionInfo'] .= $arriveDate . '-';
        }
        if ($memberInfo['intentionInfo']) {
            $memberInfo['intentionInfo'] = substr($memberInfo['intentionInfo'], 0, -1);
        }

        //优先显示简历名称
        $memberInfo['username'] = $memberInfo['name'] ?: $memberInfo['username'];
        //获取头像
        $gender               = BaseResume::findOneVal(['member_id' => $memberId], 'gender');
        $memberInfo['avatar'] = BaseMemberLoginForm::getAvatar($memberInfo['avatar'], $gender);

        //获取简历完成度
        $memberInfo['configCompletePercent'] = \Yii::$app->params['completeResumePercent'];
        $memberInfo['resumeCompletePercent'] = (int)BaseResume::getComplete($memberId);
        //获取简历待优化项目数量
        $memberInfo['unCompleteItem'] = BaseResumeComplete::getUnCompleteAmount($memberId);
        //获取简历状态
        $memberInfo['resumeStatus'] = BaseResume::findOneVal(['member_id' => $memberId], 'status') ?: '';

        //获取年龄文本
        if (!empty($memberInfo['age'])) {
            $memberInfo['age'] .= '岁';
        }
        //获取最高学历信息
        $topEducationInfo = BaseResumeEducation::findOne(['id' => $memberInfo['lastEducationId']]);
        $education        = BaseDictionary::getEducationName($topEducationInfo['education_id']);
        //        $workExperience         = BaseResumeWork::getWorkExperience($memberId);
        $memberInfo['baseInfo'] = '';
        if (!empty($memberInfo['age'])) {
            $memberInfo['baseInfo'] .= $memberInfo['age'] . ' | ';
        }
        if (!empty($education)) {
            $memberInfo['baseInfo'] .= $education . ' | ';
        }
        //        if (!empty($workExperience)) {
        //            $memberInfo['baseInfo'] .= $workExperience . ' | ';
        //        }
        if (!empty($memberInfo['baseInfo'])) {
            $memberInfo['baseInfo'] = substr($memberInfo['baseInfo'], 0, -3);
        }
        unset($memberInfo['name'], $memberInfo['age'], $memberInfo['lastEducationId'], $memberInfo['workExperience'], $memberInfo['workStatus'], $memberInfo['arriveDateType']);

        return $memberInfo;
    }

    public static function getJobToolInfo($memberId = 0)
    {
        $jobFastUrl          = BaseResume::BUY_URL_JOB_FAST;
        $competitivePowerUrl = BaseResume::BUY_URL_COMPETITIVE_POWER;
        $vipUrl              = BaseResume::BUY_URL_VIP;
        $iconUrl             = 'https://img.gaoxiaojob.com/uploads/mini/person/';

        $list = [
            // 职位刷新
            [
                'name'   => 'resumeRefresh',
                'title'  => '简历自动刷新',
                'icon'   => $iconUrl . 'tools-refresh.png',
                'url'    => $jobFastUrl,
                'status' => '0',
                'tips'   => '',
            ],
            // 简历置顶
            [
                'name'   => 'resumeTop',
                'title'  => '简历置顶',
                'icon'   => $iconUrl . 'tools-resume-top.png',
                'url'    => $jobFastUrl,
                'status' => '0',
                'tips'   => '',
            ],
            // 投递置顶
            [
                'name'   => 'deliveryTop',
                'title'  => '投递置顶',
                'icon'   => $iconUrl . 'tools-delivery-top.png',
                'url'    => $jobFastUrl,
                'status' => '0',
                'tips'   => '',
            ],
            [
                'name'  => 'competitivePower',
                'title' => '职位竞争力',
                'icon'  => $iconUrl . 'tools-job.png',
                'url'   => $competitivePowerUrl,
            ],
            // 代投服务
            [
                'name'    => 'announcementPopularity',
                // 'title'   => '公告热度', // 原标题
                'title'   => '代投服务',
                'icon'    => $iconUrl . 'tools-announcement.png',
                // 'url'     => $competitivePowerUrl, // 原链接
                'miniUrl' => '#小程序://高才优职/gy38vwZ2klAKXqj',
            ],
            // 编制查询
            [
                'name'   => 'establishmentQuery',
                'title'  => '高级筛选',
                'icon'   => $iconUrl . 'tools-establishment.png',
                'url'    => $vipUrl,
                'status' => '2',
                'tips'   => '',
            ],
            // 简历优化
            [
                'name'    => 'resumeExposure',
                // 'title'   => '简历曝光', // 原标题
                'title'   => '简历优化',
                'icon'    => $iconUrl . 'tools-exposure.png',
                'miniUrl' => '#小程序://高才优职/iMFPh6h73iQZ8xH',
            ],
            // 职场发现
            [
                'name'  => 'discovery',
                'title' => '职场发现',
                'icon'  => $iconUrl . 'discover.png',
            ],
        ];

        if (!$memberId) {
            return $list;
        }

        $vipInfo = BaseResume::getVipInfo($memberId);
        //是否有刷新的权益在生效
        $refreshData = BaseResumeEquityPackage::equityEffectData(BaseResumeEquitySetting::ID_RESUME_REFRESH,
            $vipInfo['id']);

        if (count($refreshData) > 0) {
            $list[0]['status'] = '1';
            $list[0]['tips']   = '刷新中';
        }

        $resumeTopData = BaseResumeEquityPackage::equityEffectData(BaseResumeEquitySetting::ID_RESUME_TOP,
            $vipInfo['id']);

        if (count($resumeTopData) > 0) {
            $resumeTopAmount = array_sum(array_column($resumeTopData, 'amount'));
            if ($resumeTopAmount > 0) {
                $list[1]['status'] = '1';
                $list[1]['tips']   = '可用' . $resumeTopAmount . '天';
            } else {
                $list[1]['status'] = '2';
                $list[1]['tips']   = '已用完';
            }
        }
        $deliveryTopData = BaseResumeEquityPackage::equityEffectData(BaseResumeEquitySetting::ID_DELIVERY_TOP,
            $vipInfo['id']);

        if (count($deliveryTopData) > 0) {
            $deliveryTopAmount = array_sum(array_column($deliveryTopData, 'amount'));

            if ($deliveryTopAmount > 0) {
                $list[2]['status'] = '1';
                $list[2]['tips']   = '可用' . $deliveryTopAmount . '次';
                $list[2]['url']    = $jobFastUrl . '#privilege';
            } else {
                $list[2]['status'] = '2';
                $list[2]['tips']   = '已用完';
            }
        }

        return $list;
    }

    /**
     * 获取用户统计数据
     * @param $memberId
     * @return array
     * @throws \Exception
     */
    public static function getMemberCount($memberId)
    {
        $resumeId = BaseMember::getMainId($memberId);
        $info     = BaseResume::getJobTrend($memberId);

        //获取强提醒数量
        $remindApp  = ResumeRemindApplication::getInstance();
        $remindData = $remindApp->getAll($resumeId);
        if (!empty($remindData)) {
            $info = array_merge($info, $remindData);
        }
        unset($info['job_apply_check_count'], $info['job_apply_employed_count'], $info['job_apply_invite_count'], $info['job_apply_no_pass_count'], $info['job_apply_pass_count'], $info['job_apply_wait_check_count']);

        return $info;
    }
}